"""
多当事人信息提取提示词模板
将系统提示词和用户提示词分离，便于管理和维护
"""

def get_party_extraction_system_prompt(required_party_types=None):
    """
    获取当事人信息提取的系统提示词，包含角色定义、规则和输出格式

    Args:
        required_party_types: 需要提取的当事人类型列表，如果为None则提取所有类型

    Returns:
        str: 系统提示词，包含所有规则和指导
    """
    # 根据需要的当事人类型生成针对性的提示词
    if required_party_types:
        party_types_desc = f"根据模板分析，本次只需要提取以下类型的当事人信息：{', '.join(required_party_types)}"
    else:
        party_types_desc = "需要提取所有类型的当事人信息"

    return f"""你是一位专业的法律助手，现在需要从用户提供的文本中提取当事人的信息。请按照原告、被告、第三人的顺序进行归类。

{party_types_desc}

特别重要的是，你必须将当事人明确区分为自然人和法人（或非法人组织），并分别放入不同的列表中。对每个当事人提取以下信息（如果有）：

对于自然人：
1. 姓名
2. 性别
3. 民族
4. 出生日期
5. 证件号码
6. 户籍所在地
7. 经常居住地
8. 工作单位
9. 职务
10. 联系电话
11. 证件类型

对于法人/非法人组织：
1. 名称
2. 住所地
3. 注册地
4. 法定代表人
5. 法定代表人职务
6. 法定代表人联系电话
7. 统一社会信用代码
8. 经营者信息

若某项信息文本中未提及，对应项写null。输出格式为JSON，大致结构如下：
""" + _generate_json_structure_example(required_party_types) + """

重要规则：
1. 只返回JSON格式的结果，不要有任何额外说明
2. 当事人可能有多个，请务必正确区分自然人和法人/非法人组织，并将其放入相应的列表中
3. 如果对应的值没有找到，请留空，不要留null等任何字符
4. 一般来说，原告和被告的描述开头，都有一个“原告”或“被告”字样，如果没有的，请根据上下文判断，注意不要重复
5. 【特殊情况】如果当事人重复填写了信息，同一个当事人出现多个主体，比如原告张三填写了他的自然人信息，他又有一家公司填写了公司信息（一般是个体经营者），那么只提取他的法人信息作为当事人信息，同时提取其经营者信息到经营者信息栏目中"""

def get_party_extraction_user_prompt(text):
    """
    获取当事人信息提取的用户提示词，只包含需要分析的原始文本

    Args:
        text (str): 需要分析的原始文本

    Returns:
        str: 用户提示词，包含原始文本
    """
    return f"""以下是需要提取当事人信息的文本：

{text}"""

def get_party_extraction_prompt(text):
    """
    获取当事人信息提取的提示词（保持向后兼容）

    Args:
        text (str): 需要分析的原始文本

    Returns:
        str: 用于提取当事人信息的提示词
    """
    # 为了保持向后兼容，将system prompt和user prompt合并
    system_prompt = get_party_extraction_system_prompt()
    user_prompt = get_party_extraction_user_prompt(text)
    return f"{system_prompt}\n\n{user_prompt}"